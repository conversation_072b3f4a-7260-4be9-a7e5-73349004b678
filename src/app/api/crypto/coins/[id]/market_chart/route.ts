import { NextRequest, NextResponse } from 'next/server';

const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const { id } = params;

    // Extract query parameters
    const vs_currency = searchParams.get('vs_currency') || 'usd';
    const days = searchParams.get('days') || '30';
    const interval = searchParams.get('interval') || 'daily';

    // Build the CoinGecko API URL for market chart
    const coinGeckoUrl = new URL(`${COINGECKO_API_BASE}/coins/${id}/market_chart`);
    coinGeckoUrl.searchParams.set('vs_currency', vs_currency);
    coinGeckoUrl.searchParams.set('days', days);
    coinGeckoUrl.searchParams.set('interval', interval);

    // Make the request to CoinGecko
    const response = await fetch(coinGeckoUrl.toString(), {
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();

    // Return the data with CORS headers
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error(`Error fetching market chart for ${params.id}:`, error);
    return NextResponse.json(
      { error: `Failed to fetch market chart for ${params.id}` },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
