import { NextRequest, NextResponse } from 'next/server';
import { coinGeckoRateLimiter } from '@/lib/rate-limiter';

const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

// Simple in-memory cache to reduce API calls
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 30000; // 30 seconds

async function rateLimitedFetch(url: string, options?: RequestInit) {
  await coinGeckoRateLimiter.waitForSlot();
  return fetch(url, options);
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Extract query parameters
    const vs_currency = searchParams.get('vs_currency') || 'usd';
    const order = searchParams.get('order') || 'market_cap_desc';
    const per_page = searchParams.get('per_page') || '100';
    const page = searchParams.get('page') || '1';
    const sparkline = searchParams.get('sparkline') || 'true';
    const price_change_percentage = searchParams.get('price_change_percentage') || '1h,24h,7d,30d';

    // Create cache key
    const cacheKey = `markets-${vs_currency}-${order}-${per_page}-${page}-${sparkline}-${price_change_percentage}`;

    // Check cache first
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return NextResponse.json(cached.data, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'X-Cache': 'HIT',
        },
      });
    }

    // Build the CoinGecko API URL
    const coinGeckoUrl = new URL(`${COINGECKO_API_BASE}/coins/markets`);
    coinGeckoUrl.searchParams.set('vs_currency', vs_currency);
    coinGeckoUrl.searchParams.set('order', order);
    coinGeckoUrl.searchParams.set('per_page', per_page);
    coinGeckoUrl.searchParams.set('page', page);
    coinGeckoUrl.searchParams.set('sparkline', sparkline);
    coinGeckoUrl.searchParams.set('price_change_percentage', price_change_percentage);

    // Make the request to CoinGecko with rate limiting
    const response = await rateLimitedFetch(coinGeckoUrl.toString(), {
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      // Handle rate limiting specifically
      if (response.status === 429) {
        console.warn('CoinGecko API rate limit exceeded, retrying after delay...');
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds

        // Retry once
        const retryResponse = await rateLimitedFetch(coinGeckoUrl.toString(), {
          headers: {
            'Accept': 'application/json',
          },
        });

        if (!retryResponse.ok) {
          throw new Error(`CoinGecko API error after retry: ${retryResponse.status}`);
        }

        const retryData = await retryResponse.json();

        // Cache the successful response
        cache.set(cacheKey, { data: retryData, timestamp: Date.now() });

        return NextResponse.json(retryData, {
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'X-Cache': 'MISS',
          },
        });
      }

      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();

    // Cache the successful response
    cache.set(cacheKey, { data, timestamp: Date.now() });

    // Return the data with CORS headers
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'X-Cache': 'MISS',
      },
    });
  } catch (error) {
    console.error('Error fetching cryptocurrency data:', error);

    // If we have cached data, return it even if it's slightly stale
    const cacheKey = `markets-${request.nextUrl.searchParams.get('vs_currency') || 'usd'}-${request.nextUrl.searchParams.get('order') || 'market_cap_desc'}-${request.nextUrl.searchParams.get('per_page') || '100'}-${request.nextUrl.searchParams.get('page') || '1'}-${request.nextUrl.searchParams.get('sparkline') || 'true'}-${request.nextUrl.searchParams.get('price_change_percentage') || '1h,24h,7d,30d'}`;
    const cached = cache.get(cacheKey);

    if (cached) {
      console.warn('Returning stale cached data due to API error');
      return NextResponse.json(cached.data, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'X-Cache': 'STALE',
        },
      });
    }

    return NextResponse.json(
      { error: 'Failed to fetch cryptocurrency data' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
