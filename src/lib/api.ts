import axios from 'axios';
import { CryptoCurrency, CoinDetails, HistoricalData } from '@/types/crypto';

// Use local API routes to avoid CORS issues
const API_BASE = '/api/crypto';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE,
  timeout: 15000, // Increased timeout to handle rate limiting delays
  headers: {
    'Accept': 'application/json',
  },
});

// Add request interceptor for rate limiting
api.interceptors.request.use((config) => {
  // Add a small delay to respect rate limits
  return new Promise((resolve) => {
    setTimeout(() => resolve(config), 200); // Increased delay
  });
});

// Add response interceptor for better error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 429) {
      // Rate limit exceeded, wait and retry once
      console.warn('Rate limit exceeded, retrying after delay...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      return api.request(error.config);
    }
    return Promise.reject(error);
  }
);

export class CryptoAPI {
  /**
   * Fetch top cryptocurrencies by market cap
   */
  static async getTopCryptocurrencies(
    limit: number = 100,
    page: number = 1,
    currency: string = 'usd'
  ): Promise<CryptoCurrency[]> {
    try {
      const response = await api.get('/markets', {
        params: {
          vs_currency: currency,
          order: 'market_cap_desc',
          per_page: limit,
          page: page,
          sparkline: true,
          price_change_percentage: '1h,24h,7d,30d',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching top cryptocurrencies:', error);
      throw new Error('Failed to fetch cryptocurrency data');
    }
  }

  /**
   * Fetch detailed information about a specific cryptocurrency
   */
  static async getCoinDetails(coinId: string): Promise<CoinDetails> {
    try {
      const response = await api.get(`/coins/${coinId}`, {
        params: {
          localization: false,
          tickers: false,
          market_data: true,
          community_data: true,
          developer_data: true,
          sparkline: false,
        },
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching details for ${coinId}:`, error);
      throw new Error(`Failed to fetch details for ${coinId}`);
    }
  }

  /**
   * Fetch historical price data for a cryptocurrency
   */
  static async getHistoricalData(
    coinId: string,
    days: number = 30,
    currency: string = 'usd'
  ): Promise<HistoricalData> {
    try {
      const response = await api.get(`/coins/${coinId}/market_chart`, {
        params: {
          vs_currency: currency,
          days: days,
          interval: days <= 1 ? 'hourly' : days <= 90 ? 'daily' : 'weekly',
        },
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching historical data for ${coinId}:`, error);
      throw new Error(`Failed to fetch historical data for ${coinId}`);
    }
  }

  /**
   * Search for cryptocurrencies by name or symbol
   */
  static async searchCryptocurrencies(query: string): Promise<any[]> {
    try {
      const response = await api.get('/search', {
        params: {
          query: query,
        },
      });
      return response.data.coins || [];
    } catch (error) {
      console.error('Error searching cryptocurrencies:', error);
      throw new Error('Failed to search cryptocurrencies');
    }
  }

  /**
   * Fetch global cryptocurrency market data
   */
  static async getGlobalData(): Promise<any> {
    try {
      const response = await api.get('/global');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching global data:', error);
      throw new Error('Failed to fetch global market data');
    }
  }

  /**
   * Fetch trending cryptocurrencies
   */
  static async getTrendingCryptocurrencies(): Promise<any[]> {
    try {
      const response = await api.get('/search/trending');
      return response.data.coins || [];
    } catch (error) {
      console.error('Error fetching trending cryptocurrencies:', error);
      throw new Error('Failed to fetch trending cryptocurrencies');
    }
  }
}

export default CryptoAPI;
