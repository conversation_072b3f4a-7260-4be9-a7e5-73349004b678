/**
 * Simple rate limiter utility for CoinGecko API calls
 */

interface RateLimiterConfig {
  maxRequests: number;
  windowMs: number;
  minInterval: number;
}

class RateLimiter {
  private requests: number[] = [];
  private lastRequestTime = 0;
  private config: RateLimiterConfig;

  constructor(config: RateLimiterConfig) {
    this.config = config;
  }

  async waitForSlot(): Promise<void> {
    const now = Date.now();
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(time => now - time < this.config.windowMs);
    
    // Check if we've exceeded the rate limit
    if (this.requests.length >= this.config.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.config.windowMs - (now - oldestRequest);
      if (waitTime > 0) {
        console.warn(`Rate limit reached, waiting ${waitTime}ms`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        return this.waitForSlot(); // Recursive call to check again
      }
    }
    
    // Ensure minimum interval between requests
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.config.minInterval) {
      const waitTime = this.config.minInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    // Record this request
    this.requests.push(Date.now());
    this.lastRequestTime = Date.now();
  }
}

// CoinGecko free tier limits: 10-50 calls/minute
export const coinGeckoRateLimiter = new RateLimiter({
  maxRequests: 10, // Conservative limit
  windowMs: 60000, // 1 minute
  minInterval: 1000, // 1 second between requests
});

export default RateLimiter;
