{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/app/api/crypto/coins/%5Bid%5D/market_chart/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nconst COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const { id } = params;\n\n    // Extract query parameters\n    const vs_currency = searchParams.get('vs_currency') || 'usd';\n    const days = searchParams.get('days') || '30';\n    const interval = searchParams.get('interval') || 'daily';\n\n    // Build the CoinGecko API URL for market chart\n    const coinGeckoUrl = new URL(`${COINGECKO_API_BASE}/coins/${id}/market_chart`);\n    coinGeckoUrl.searchParams.set('vs_currency', vs_currency);\n    coinGeckoUrl.searchParams.set('days', days);\n    coinGeckoUrl.searchParams.set('interval', interval);\n\n    // Make the request to CoinGecko\n    const response = await fetch(coinGeckoUrl.toString(), {\n      headers: {\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error(`CoinGecko API error: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Return the data with CORS headers\n    return NextResponse.json(data, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n      },\n    });\n  } catch (error) {\n    console.error(`Error fetching market chart for ${params.id}:`, error);\n    return NextResponse.json(\n      { error: `Failed to fetch market chart for ${params.id}` },\n      { \n        status: 500,\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n        },\n      }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,qBAAqB;AAEpB,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,EAAE,EAAE,EAAE,GAAG;QAEf,2BAA2B;QAC3B,MAAM,cAAc,aAAa,GAAG,CAAC,kBAAkB;QACvD,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;QACzC,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QAEjD,+CAA+C;QAC/C,MAAM,eAAe,IAAI,IAAI,GAAG,mBAAmB,OAAO,EAAE,GAAG,aAAa,CAAC;QAC7E,aAAa,YAAY,CAAC,GAAG,CAAC,eAAe;QAC7C,aAAa,YAAY,CAAC,GAAG,CAAC,QAAQ;QACtC,aAAa,YAAY,CAAC,GAAG,CAAC,YAAY;QAE1C,gCAAgC;QAChC,MAAM,WAAW,MAAM,MAAM,aAAa,QAAQ,IAAI;YACpD,SAAS;gBACP,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,MAAM,EAAE;QAC3D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,oCAAoC;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;QAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,CAAC,iCAAiC,EAAE,OAAO,EAAE,EAAE;QAAC,GACzD;YACE,QAAQ;YACR,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IAEJ;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}